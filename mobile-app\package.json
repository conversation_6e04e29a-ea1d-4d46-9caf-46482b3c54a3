{"name": "qaaqconnect-mobile", "version": "2.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "tunnel": "expo start --tunnel", "clear": "expo start --clear", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "lint": "expo lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/bottom-tabs": "^7.1.6", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.2", "@tanstack/react-query": "^5.61.3", "expo": "~53.0.20", "expo-camera": "^16.1.11", "expo-font": "^13.3.2", "expo-location": "~18.0.3", "expo-status-bar": "~2.0.0", "react": "18.3.1", "react-native": "0.76.5", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-paper": "^5.13.0", "react-native-reanimated": "~3.16.3", "react-native-safe-area-context": "~4.12.0", "react-native-screens": "~4.1.0", "react-native-svg": "~15.8.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~18.3.12", "typescript": "^5.7.2"}, "private": true}